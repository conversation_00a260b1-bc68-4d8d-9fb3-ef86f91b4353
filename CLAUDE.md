# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `npm run dev` - Start development server with hot reload using nodemon
- `npm run build` - Compile TypeScript and copy email templates to dist/
- `npm run start` - Build and start production server
- `npm run test` - Run Jest tests with coverage
- `npm run test-debug` - Run tests with Node.js inspector for debugging

### Database Commands
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Run database seeders
- `npm run db:gen -- <migration-name>` - Generate new migration file
- `npm run db:sync` - Sync models with database

### Production Commands
- `npm run start-server` - Start server with PM2 (4 instances, 1GB memory limit)
- `npm run restart-server` - Restart PM2 server instances

### Utility Scripts
- `npm run create-program-pdfs` - Generate PDF files for programs
- `npm run import-data` - Import staff and application data
- `npm run send-email-test` - Test email functionality
- `npm run update-filter-data` - Update filters to camelCase format

## Architecture Overview

### Technology Stack
- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Express.js with CORS, body-parser, file upload
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT tokens with role-based access control
- **File Storage**: AWS S3 integration
- **Email**: SendGrid and AWS SES
- **Process Management**: PM2 in production
- **Testing**: Jest with Supertest

### Core Domain Models
This is a grant management system with the following main entities:

1. **Clients** - Organizations that use the system (municipalities, nonprofits)
2. **Employees** - System users with roles (Admin, Manager, Researcher, Analyst)
3. **Programs** - Grant opportunities from funders
4. **Applications** - Client applications to grant programs
5. **Awards** - Approved applications that become active grants
6. **Projects** - Work that can span multiple awards
7. **Budget/Payments/Reports** - Financial tracking and compliance

### User Roles & Permissions
- **Millennium Admin**: Full system access
- **Millennium Manager**: Management operations
- **Millennium Researcher**: Research and application management
- **Millennium Analyst**: View and analysis only
- **Client Admin**: Client organization admin access
- **Client Analyst**: Client organization analyst access

### API Structure
- RESTful endpoints with standard HTTP methods
- Nested resources for complex entities (e.g., `/awards/:id/budget-entries`)
- Bulk operations for efficiency (`/resource/multiple`)
- Export functionality (CSV/XLSX) for most resources
- Search and filtering capabilities across entities

### Database Patterns
- Multi-tenant architecture with client isolation
- Comprehensive audit logging via ActionLogs
- JSON fields for flexible custom data
- Complex status workflows for applications and awards
- Financial tracking with budget entries and version control

### Key File Locations
- **Models**: `src/models/` - Sequelize models and type definitions
- **Controllers**: `src/controllers/` - API endpoint handlers
- **Routes**: `src/routes/` - Express route definitions
- **Services**: `src/services/` - Business logic layer
- **Validations**: `src/validations/` - Joi validation schemas
- **Migrations**: `src/db/migrations/` - Database schema changes
- **Tests**: `src/tests/` - Jest test files with mock data
- **Email Templates**: `src/email_templates/` - HTML email templates

### Environment Variables
The application requires configuration via environment variables:
- Database connection: `DB_HOST`, `DB_NAME`, `DB_USER`, `DB_PASSWORD`
- JWT secret: `JWT_SECRET`
- AWS credentials for S3 and SES
- SendGrid API key
- Node environment: `NODE_ENV`

### Development Workflows
- Code uses TypeScript with strict typing
- ESLint and Prettier for code formatting (enforced via pre-commit hooks)
- Husky and lint-staged for Git hooks
- Database migrations for schema changes
- Comprehensive test coverage with mock data

### Security Considerations
- JWT token validation on protected routes
- Role-based authorization middleware
- SQL injection protection via Sequelize ORM
- File upload validation and S3 storage
- Password hashing and secure user invitations

### Monitoring & Logging
- Winston logger for structured logging
- Morgan for HTTP request logging
- PM2 for process monitoring in production
- Cron scheduler for automated tasks (email reminders, etc.)
- Request logging middleware for debugging
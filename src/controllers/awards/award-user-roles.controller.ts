import { Request, Response } from 'express';
import awardService from '../../services/award.service';

require('dotenv').config();

const httpStatus = require('http-status');
const dayjs = require('dayjs');
const advancedFormat = require('dayjs/plugin/advancedFormat');

const catchAsync = require('../../helpers/catch-async');

dayjs.extend(advancedFormat);

/**
 * Get all AwardUserRoles
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
export const listUserRoles = catchAsync(async (req: Request, res: Response) => {
  const { id: awardId } = req.params;
  const { query } = req;
  const data = await awardService.getAllUserRoles(query, awardId);

  res.status(httpStatus.OK).json(data);
});

/**
 * Get AwardUserRole by ID
 */
export const getUserRole = catchAsync(async (req: Request, res: Response) => {
  const { userRoleId } = req.params;

  const data = await awardService.getUserRoleById(userRoleId);

  res.status(httpStatus.OK).json(data);
});

/**
 * Create a new AwardUserRole
 */
export const createUserRoles = catchAsync(async (req: Request, res: Response) => {
  const { id: awardId } = req.params;
  req.body.awardId = awardId;

  const data = await awardService.createUserRole(req.body, req.user, awardId);

  res.status(httpStatus.CREATED).json(data);
});

/**
 * Update an existing AwardUserRole
 */
export const updateUserRoles = catchAsync(async (req: Request, res: Response) => {
  const { userRoleId, id: awardId } = req.params;
  req.body.awardId = awardId;

  const data = await awardService.updateUserRole(userRoleId, req.body, req.user, awardId);

  res.status(httpStatus.OK).json(data);
});

/**
 * Delete an AwardUserRole
 */
export const deleteUserRoles = catchAsync(async (req: Request, res: Response) => {
  const { userRoleId, id: awardId } = req.params;

  await awardService.deleteUserRole(userRoleId, req.user, awardId);

  res.status(httpStatus.NO_CONTENT).send();
});

export default {
  listUserRoles,
  getUserRole,
  createUserRoles,
  updateUserRoles,
  deleteUserRoles,
};

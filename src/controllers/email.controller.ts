import { Op } from 'sequelize';
import httpStatus from 'http-status';
import { sendEmailLetter, parseHTMLTemplate } from '../helpers/mail.helper';
import db from '../models';

const { Employee, Client } = db;

const catchAsync = require('../helpers/catch-async');

const sendContactEmail = catchAsync(async (req: any, res: any) => {
  const { subject, message, clientId, includeClientDirector } = req.body;
  const { user } = req;

  if (!subject || !message) {
    return res.status(httpStatus.BAD_REQUEST).send({ message: 'Missing required fields' });
  }

  const client = await Client.findByPk(clientId);

  if (!client) {
    return res.status(httpStatus.BAD_REQUEST).send({ message: 'Client not found' });
  }

  // Get primary user email
  const toUser = await Employee.findOne({
    where: { [Op.or]: [{ id: client.primaryUserId }, { primaryClientId: clientId }] },
    attributes: ['email'],
  });

  // Get client director email if requested
  let directorEmail = null;
  if (includeClientDirector) {
    const directorRecord = await db.EmployeeClient.findOne({
      where: {
        clientId,
        isDirector: true,
      },
    });

    if (directorRecord && directorRecord.employeeId) {
      const directorUser = await Employee.findByPk(directorRecord.employeeId);
      if (directorUser && directorUser.email) {
        directorEmail = directorUser.email;
      }
    }
  }

  // Prepare recipients list
  const recipients = [toUser.email || '<EMAIL>'];
  if (directorEmail) {
    recipients.push(directorEmail);
  }

  // Prepare email content
  const htmlContent = parseHTMLTemplate(`${__dirname}/../email_templates/contact-support.html`, {
    message,
    clientId,
  });

  const mailOptions = {
    to: recipients,
    from: process.env.SERVICE_MAILER_EMAIL,
    replyTo: user.email,
    subject: `Support Request: ${subject}`,
    plainver: message,
    htmlver: htmlContent,
  };

  console.log('sendContactEmail - mailOptions', mailOptions);

  // Send email
  await sendEmailLetter(mailOptions);

  return res.status(httpStatus.OK).send({ message: 'Email sent successfully' });
});

module.exports = {
  sendContactEmail,
};

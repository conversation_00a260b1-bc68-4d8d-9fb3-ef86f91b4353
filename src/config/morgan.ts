import morgan, { StreamOptions } from 'morgan';
import { Request, Response } from 'express';
import { IncomingMessage, ServerResponse } from 'http';
import logger from './logger';

require('dotenv').config();

const { NODE_ENV } = process.env;

morgan.token('message', (req: IncomingMessage, res: ServerResponse): string => {
  const response = res as Response;
  return response.locals.errorMessage || '';
});

const getIpFormat = (): string => (NODE_ENV === 'production' ? ':remote-addr - ' : '');
const successResponseFormat = `${getIpFormat()}:method :url :status - :response-time ms`;
const errorResponseFormat = `${getIpFormat()}:method :url :status - :response-time ms - message: :message`;

const successHandler = morgan(successResponseFormat, {
  skip: (req: IncomingMessage, res: ServerResponse) => (res as Response).statusCode >= 400,
  stream: {
    write: (message: string) => logger.info(message.trim()),
  } as StreamOptions,
});

const errorHandler = morgan(errorResponseFormat, {
  skip: (req: IncomingMessage, res: ServerResponse) => (res as Response).statusCode < 400,
  stream: {
    write: (message: string) => logger.error(message.trim()),
  } as StreamOptions,
});

export default {
  successHandler,
  errorHandler,
};

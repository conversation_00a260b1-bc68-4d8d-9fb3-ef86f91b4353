/*
 * Constants for storage of stuff that isn't in the database.
 */

// import { preferences } from "joi";

const strings = {
  applications: {
    headers: [
      'Assigned To',
      'Client',
      'Funder',
      'Grant Program Name',
      'Start Date',
      'Due Date',
      'Status',
      'Funding Amount',
      'Source',
      'Category',
      'Client Notified Date',
      'Grant Submitted Date',
      'Grant Decided Date',
      'Notes / Status',
      'Match Requirements',
      'Department Notified',
      'Period of Performance',
      'Grant Purpose',
      'Estimated Response',
      'Grant Summary File Name',
    ],
    statusNames: [
      'Not Started',
      'Client Notified',
      'In Progress',
      'Grant Submitted',
      'Grant Awarded',
      'Grant Not Awarded',
      'Not Pursuing',
      'Discontinued',
      'Client Submitting',
      'Not Eligible',
    ],
    statuses: {
      notStarted: '0',
      clientNotified: '1',
      inProgress: '2',
      grantSubmitted: '3',
      grantAwarded: '4',
      grantNotAwarded: '5',
      notPursuing: '6',
      discontinued: '7',
      clientSubmitting: '8',
      notEligible: '9',
    },
    categoryNames: [
      'Environmental',
      'Transportation',
      'Health',
      'Public Safety',
      'Parks & Open Space',
      'Library',
      'Recreation',
      'Historic Preservation',
      'Community Development',
      'Economic Development',
      'Education',
      'Justice',
      'Housing',
      'Arts & Humanities',
      'Youth',
      'Disaster Recovery',
      'Workforce Development',
      'General',
    ],
    sourceNames: ['Federal', 'State', 'Regional', 'County', 'Local', 'Philanthropic', 'Other'],
  },
  projects: {
    categories: [
      'Arts & Humanities',
      'Community Development',
      'Disaster Recovery',
      'Economic Development',
      'Education',
      'Environmental',
      'General',
      'Health',
      'Historic Preservation',
      'Housing',
      'Justice',
      'Library',
      'Parks & Open Space',
      'Public Safety',
      'Recreation',
      'Transportation',
      'Workforce Development',
      'Youth',
    ],
    sources: ['Bond', 'Budget', 'Grant', 'Loan'],
  },
  awards: {
    statuses: {
      applicationRequired: 0,
      awardLetterRequired: 1,
      resolutionRequired: 2,
      initialAgreementRequired: 3,
      budgetRequired: 4,
      reportScheduleRequired: 5,
      contractMaterialRequired: 6,
      encumbered: 7,
      approval: 8,
      active: 9,
      reportsRequired: 10,
      complianceRequired: 11,
      closeout: 12,
      closed: 13,
    },
    statusStrings: {
      applicationRequired: 'applicationRequired',
      awardLetterRequired: 'awardLetterRequired',
      resolutionRequired: 'resolutionRequired',
      initialAgreementRequired: 'initialAgreementRequired',
      budgetRequired: 'budgetRequired',
      reportScheduleRequired: 'reportScheduleRequired',
      contractMaterialRequired: 'contractMaterialRequired',
      encumbered: 'encumbered',
      approval: 'approval',
      active: 'active',
      reportsRequired: 'reportsRequired',
      complianceRequired: 'complianceRequired',
      closeout: 'closeout',
      closed: 'closed',
    },
    statusTimelineStrings: {
      awarded: 'awarded',
      applicationRequired: 'applicationRequired',
      awardLetterRequired: 'awardLetterRequired',
      resolutionRequired: 'resolutionRequired',
      initialAgreementRequired: 'initialAgreementRequired',
      appropriated: 'appropriated',
      budgetRequired: 'budgetRequired',
      reportScheduleRequired: 'reportScheduleRequired',
      contractMaterialRequired: 'contractMaterialRequired',
      encumbered: 'encumbered',
      approval: 'approval',
      active: 'active',
      reportsRequired: 'reportsRequired',
      complianceRequired: 'complianceRequired',
      closeout: 'closeout',
      closed: 'closed',
    },
    budgetMatchTypes: ['existingGrant', 'inKind', 'other'],
    awardRoles: [
      'primaryOrganizationalContact',
      'projectDirector',
      'departmentDirector',
      'financeContact',
      'resolutionOwner',
      'purchasing',
    ],
    defaultAwardRoles: [
      'primaryOrganizationalContact',
      // 'departmentDirector',
      'financeContact',
      'resolutionOwner',
      'purchasing',
    ],
    paymentTypes: ['advance', 'reimbursement'],
    paymentStatuses: ['paid', 'reimbursed'],
    reportTypes: ['financial', 'programmatic', 'combined'],
    reportTypeStrings: {
      financial: 'financial',
      programmatic: 'programmatic',
      combined: 'combined',
    },
    fileCategories: [
      'initialApplication',
      'initialAwardLetter',
      'initialResolution',
      'initialAgreement',
      'requiredContractMaterial',
      'optionalContractMaterial',
      'report',
      'other',
    ],
    notificationTypes: [
      'approvalRequest',
      'approved',
      'rejected',
      'documentReminder',
      'reportReminder',
      'reportDue',
      'reportLate',
    ],
    budgetEntryCategories: [
      'Personnel',
      'Fringe Benefits',
      'Travel',
      'Equipment',
      'Supplies',
      'Contractual',
      'Construction',
      'Other',
      'Total Direct Charges',
      'Indirect Charges',
    ],
    headers: [
      'Client',
      'Funder',
      'Grant Program Name',
      'Assigned To',
      'Department',
      'Status',
      'Start Date',
      'End Date',
      'Next Report Due',
      'Total Award Amount',
      'Balance',
      'Expended',
      'Payments Requested',
      'Payments Received',
    ],
    preferences: [
      'awardEmailNotification', // Send email notifications for awards
      'awardBatchEmails', // Send batch emails in advance of events every...
      'awardReminders', // Send reminders every...
      'grantIdentifier', // Grant identifier
    ],
    preferencesOptions: ['day', 'week', 'biWeekly', 'month', 'endOfMonth', 'year'],
  },
  users: {
    userTypes: {
      millenniumAdmin: 'millenniumAdmin',
      millenniumManager: 'millenniumManager',
      millenniumResearcher: 'millenniumResearcher',
      millenniumAnalyst: 'millenniumAnalyst',
      userAdmin: 'userAdmin',
      userAnalyst: 'userAnalyst',
    },
  },
};

export default strings;
module.exports = strings;

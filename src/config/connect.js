const { Sequelize } = require('sequelize');

const dbConfig = require('./config');

const connection = new Sequelize(
  dbConfig.development.database,
  dbConfig.development.username,
  dbConfig.development.password,
  {
    host: dbConfig.development.host,
    dialect: dbConfig.development.dialect,
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    },
    // query: { raw: true },
    // benchmark: true,
    // logging: true,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  }
);

// connection.sync({ logging: console.log });

module.exports = connection;

// connection.authenticate().finally(() => {
//   console.log(`Connection has been established successfully to ${dbConfig.development.database}.`);
// });

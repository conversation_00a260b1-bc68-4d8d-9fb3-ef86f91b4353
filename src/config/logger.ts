import winston, { transports } from 'winston';
import fs from 'fs';
import * as path from 'path';

require('dotenv').config();

const { NODE_ENV, DATADOG_API_KEY, DATADOG_APPLICATION_NAME } = process.env;

const enumerateErrorFormat = winston.format((info) => {
  if (info instanceof Error) {
    Object.assign(info, { message: info.stack });
  }
  return info;
});

const httpTransportOptions = {
  host: 'http-intake.logs.us5.datadoghq.com',
  path: `/api/v2/logs?dd-api-key=${DATADOG_API_KEY}&ddsource=nodejs&service=${DATADOG_APPLICATION_NAME}`,
  ssl: true,
};

if (!fs.existsSync(path.join(__dirname, '../log'))) {
  fs.mkdirSync(path.join(__dirname, '../log'));
}

const logger = winston.createLogger({
  level: NODE_ENV === 'development' ? 'debug' : 'info',
  format: winston.format.combine(
    enumerateErrorFormat(),
    NODE_ENV === 'development' ? winston.format.colorize() : winston.format.uncolorize(),
    winston.format.splat(),
    winston.format.printf(({ level, message }) => `${level}: ${message}`)
  ),
  transports: [
    new winston.transports.Console({
      stderrLevels: ['debug', 'info', 'warn', 'error'],
    }),
    new transports.Http(httpTransportOptions),
  ],
});

// Configure winston logger
const schedulerLogPath = path.join(__dirname, '../log/schedule.log');
// console.log(`Log path: ${schedulerLogPath}`);

// Check if directory exists
// const logDir = path.dirname(schedulerLogPath);
// if (!fs.existsSync(logDir)) {
//   console.error(`Log directory does not exist: ${logDir}`);
// } else {
//   console.log(`Log directory exists: ${logDir}`);
// }

// // Check write permissions
// try {
//   fs.accessSync(logDir, fs.constants.W_OK);
//   console.log(`Write permission to log directory: ${logDir}`);
// } catch (err) {
//   console.error(`No write permission to log directory: ${logDir}`);
// }

export const scheduleLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    enumerateErrorFormat(),
    winston.format.uncolorize(),
    winston.format.splat(),
    winston.format.printf(({ level, message }) => `${level}: ${message}`)
  ),
  transports: [
    new winston.transports.Console({
      stderrLevels: ['debug', 'info', 'warn', 'error'],
    }),
    new winston.transports.File({
      filename: schedulerLogPath,
      level: 'info', // Log level, e.g., 'info', 'error', etc.
      maxsize: 5 * 1024 * 1024, // 5 MB max file size before rotating
      maxFiles: 5, // Keep up to 5 rotated log files
      format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
    }),
  ],
});

export default logger;

import { Request } from 'express';
import db from '../models';

const { NotificationPreference, Filter, AwardBudgetEntry } = db;
const strings = require('./strings');

const {
  users: { userTypes },
} = strings;

const admin = `${userTypes.millenniumAdmin}`;
const manager = `${userTypes.millenniumManager}`;
const researcher = `${userTypes.millenniumResearcher}`;
const analyst = `${userTypes.millenniumAnalyst}`;
const clientAdmin = `${userTypes.userAdmin}`;
const clientAnalyst = `${userTypes.userAnalyst}`;

const getUserType = (req: Request) => {
  // If the authentication checker middleware fails, then this request fails as well,
  // so we are guaranteed to have a user.

  const { user } = req;

  if (!user) return null;

  // let userType;
  // if (typeof user.userType === 'number')
  //   userType = Employees.getUserType(parseInt(user.userType, 10));
  // else
  // const userType = user.userType;

  return user.userType;
};

const access = {
  application: {
    create: {
      allowed: async (req: Request) => [admin, manager, researcher].includes(getUserType(req)),
    },
    update: {
      allowed: async (req: Request) => [admin, manager, researcher].includes(getUserType(req)),
    },
  },
  client: {
    list: {
      allowed: async (req: Request) =>
        [admin, manager, researcher, analyst].includes(getUserType(req)),
    },
    create: {
      allowed: async (req: Request) => [admin, manager].includes(getUserType(req)),
    },
    update: {
      allowed: async (req: Request) => [admin, manager, analyst].includes(getUserType(req)),
    },
    delete: {
      allowed: async (req: Request) => [admin, manager].includes(getUserType(req)),
    },
  },
  email: {
    send: {
      allowed: async (req: Request) => [clientAdmin, clientAnalyst].includes(getUserType(req)),
    },
  },
  award: {
    create: {
      allowed: async (req: Request) =>
        [admin, manager, clientAdmin, clientAnalyst].includes(getUserType(req)),
    },
    update: {
      allowed: async (req: Request) =>
        [admin, manager, clientAdmin, clientAnalyst].includes(getUserType(req)),
      // const awardId = req.params.id || req.body.awardId || req.body.id;
      // const allowedAwardRoles = await AwardUserRole.findOne({
      //   where: {
      //     awardId,
      //     userId: req.user.id,
      //     role: {
      //       [Op.in]: ['primaryOrganizationalContact', 'projectDirector'],
      //     },
      //   },
      // });
      // return !!allowedAwardRoles || [admin, manager, researcher].includes(getUserType(req));
    },
    delete: {
      allowed: async (req: Request) =>
        [admin, manager, clientAdmin, clientAnalyst].includes(getUserType(req)),
      // const awardId = req.params.id || req.body.awardId || req.body.id;
      // const allowedAwardRoles = await AwardUserRole.findOne({
      //   where: {
      //     awardId,
      //     userId: req.user?.id,
      //     role: {
      //       [Op.in]: ['primaryOrganizationalContact', 'projectDirector'],
      //     },
      //   },
      // });
      // return !!allowedAwardRoles || [admin, manager, researcher].includes(getUserType(req));
    },
    approve: {
      allowed: async (req: Request) => [admin, clientAdmin].includes(getUserType(req)),
    },
    updateBudget: {
      allowed: async (req: Request) => {
        const awardId = req.params.id;
        let budgetIds = [req.body.id];
        if (Array.isArray(req.body)) {
          budgetIds = req.body.map((item) => item.id);
        }
        const budgetEntries: any[] = await AwardBudgetEntry.findAll({
          where: {
            awardId,
            id: budgetIds,
          },
        });

        if (budgetEntries.length !== budgetIds.length) {
          return false;
        }

        return [admin, manager, clientAdmin, clientAnalyst].includes(getUserType(req));

        // const allowedAwardRoles = await AwardUserRole.findOne({
        //   where: {
        //     awardId,
        //     userId: req.user.id,
        //     role: {
        //       [Op.in]: ['primaryOrganizationalContact', 'projectDirector'],
        //     },
        //   },
        // });
        // return !!allowedAwardRoles || [admin, manager, researcher].includes(getUserType(req));
      },
    },
  },
  program: {
    create: {
      allowed: async (req: Request) => [admin, manager, researcher].includes(getUserType(req)),
    },
    update: {
      allowed: async (req: Request) => [admin, manager, researcher].includes(getUserType(req)),
    },
  },
  employee: {
    create: {
      allowed: async (req: Request) => ![clientAnalyst, analyst].includes(getUserType(req)),
    },
    update: {
      allowed: async (req: Request) => ![clientAnalyst, analyst].includes(getUserType(req)),
    },
    delete: {
      allowed: async (req: Request) => [admin, manager, clientAdmin].includes(getUserType(req)),
    },
    invite: {
      allowed: async (req: Request) => ![analyst, clientAnalyst].includes(getUserType(req)),
    },

    updatePreference: {
      allowed: async (req: Request) => {
        const data = req.body;

        async function verifyAuthority(entry: any) {
          const preference = await NotificationPreference.findByPk(entry?.id);

          return (
            preference.userId === req.user?.id ||
            req.user?.userType === admin ||
            req.user?.userType === clientAdmin ||
            req.user?.userType === manager
          );
        }

        const allowedEntries: boolean[] = await Promise.all(data.map(verifyAuthority));

        return allowedEntries.every((entry) => entry);
      },
    },
    viewPreference: {
      allowed: async (req: Request) =>
        req.user?.userType === admin ||
        req.user?.userType === clientAdmin ||
        req.user?.userType === manager,
    },
  },

  filter: {
    update: {
      allowed: async (req: Request) => {
        const filter = await Filter.findByPk(req.body.id);
        return filter.userId === req.user?.id || req.user?.userType === admin;
      },
    },
    delete: {
      allowed: async (req: Request) => {
        const filter = await Filter.findByPk(req.body.id);
        return filter.userId === req.user?.id || req.user?.userType === admin;
      },
    },
  },

  project: {
    create: {
      allowed: async (req: Request) => ![researcher, analyst].includes(getUserType(req)),
    },
    update: {
      allowed: async (req: Request) => ![researcher, analyst].includes(getUserType(req)),
    },
    delete: {
      allowed: async (req: Request) => ![researcher, analyst].includes(getUserType(req)),
    },
  },
};
const rules = new Map(Object.entries(access));

module.exports = {
  rules,
};

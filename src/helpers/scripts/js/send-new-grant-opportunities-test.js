// node src/helpers/scripts/js/send-new-grant-opportunities-test.js

require('dotenv').config();
const dayjs = require('dayjs');
const { parseHTMLTemplate, sendEmailLetter } = require('../../mail.helper.js');

// Mock program details for testing
const mockProgramDetails = [
  {
    programName: 'Rural Development Grant',
    fileName: 'Rural-Dev-2024.pdf',
    grantSummary: 'Funding for rural infrastructure development projects',
    category: 'Infrastructure',
    states: 'All States',
    programLink: `${process.env.PUBLIC_URL}/programs/123`,
  },
  {
    programName: 'Education Innovation Fund',
    fileName: 'Edu-Innovation-2024.pdf',
    grantSummary: 'Support for innovative educational programs in underserved communities',
    category: 'Education',
    states: 'NY, NJ, CT',
    programLink: `${process.env.PUBLIC_URL}/programs/456`,
  },
];

async function sendTestEmail() {
  try {
    // Parse the email template with mock data
    const htmlContent = parseHTMLTemplate(
      `${__dirname}/../../email_templates/new-grant-opportunity.html`,
      {
        programs: mockProgramDetails,
        viewMoreLink: `${process.env.PUBLIC_URL}/`,
      }
    );

    // Send the test email
    await sendEmailLetter({
      from: process.env.SERVICE_MAILER_EMAIL,
      to: process.env.TEST_EMAIL_TO || '<EMAIL>', // Change this to your test email
      subject: 'New Grant Opportunities Available - TEST',
      plainver:
        'New grant opportunities have been posted. Please check your GrantTrack portal for details.',
      htmlver: htmlContent,
    });

    console.log(
      `Test email sent successfully to ${
        process.env.TEST_EMAIL_TO || '<EMAIL>'
      }`
    );
  } catch (error) {
    console.error('Error sending test email:', error);
  }
}

sendTestEmail();

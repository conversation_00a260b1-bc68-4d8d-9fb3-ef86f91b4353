/* eslint-disable import/extensions */
/* eslint-disable no-await-in-loop */

import { S3Client, HeadObjectCommand } from '@aws-sdk/client-s3';
import { Sequelize } from 'sequelize';
import * as fs from 'fs/promises';
import * as path from 'path';

const {
  s3Upload,
  s3Delete,
  convertToPDF,
  generateFileSummary,
  getFileStream,
} = require('../../file.helper.ts');

require('dotenv').config();

interface Program {
  id: number;
  summary_file: string;
}

interface AlternativeFileResult {
  buffer: Buffer;
  source: 'local' | 's3';
  path: string;
}

const s3 = new S3Client({ region: process.env.AWS_REGION });

// Initialize Sequelize
const sequelize = new Sequelize(
  process.env.DB_NAME!,
  process.env.DB_USERNAME!,
  process.env.DB_PASSWORD!,
  {
    host: process.env.DB_HOST,
    dialect: 'postgres',
    logging: false,
  }
);

async function findFileFromAlternativeSources(
  originalFileName: string,
  program: Program
): Promise<AlternativeFileResult | null> {
  console.log(`🔍 DEBUG: Starting file search for originalFileName: "${originalFileName}"`);

  const fileName = originalFileName.split('-').slice(1).join('-').substring(2); // Remove timestamp and "0_" prefix
  console.log(`🔍 DEBUG: Processed fileName: "${fileName}"`);
  console.log(`🔍 DEBUG: Program ID: ${program.id}`);

  // Define potential file locations - fix the path to go to /app instead of /app/src
  const localPaths = [
    path.join(__dirname, '../../../../temp', fileName),
    path.join(__dirname, '../../../../temp', originalFileName),
    path.join(__dirname, '../../../../summaries', fileName),
    path.join(__dirname, '../../../../summaries', originalFileName),
    path.join(__dirname, '../../../../uploads', fileName),
    path.join(__dirname, '../../../../uploads', originalFileName),
  ];

  console.log(`🔍 DEBUG: Checking ${localPaths.length} local paths:`);
  localPaths.forEach((filePath, index) => console.log(`  ${index + 1}. ${filePath}`));

  // Try to find file locally first
  for (const localPath of localPaths) {
    try {
      await fs.access(localPath);
      console.log(`✓ Found file locally: ${localPath}`);
      const fileBuffer = await fs.readFile(localPath);
      return { buffer: fileBuffer, source: 'local', path: localPath };
    } catch (error) {
      console.log(`❌ File not found at: ${localPath}`);
    }
  }

  // Check if we can find timestamped versions in local summaries directory
  try {
    const summariesDir = path.join(__dirname, '../../../../summaries');
    console.log(`🔍 DEBUG: Checking summaries directory: ${summariesDir}`);

    const files = await fs.readdir(summariesDir);
    console.log(`🔍 DEBUG: Found ${files.length} files in summaries directory:`);

    // Look for files that match the pattern: timestamp-0_filename
    console.log(`🔍 DEBUG: Looking for files containing: "${fileName}"`);

    const matchingFile = files.find((file) => {
      // Check if file follows the timestamped pattern
      if (file.includes('-0_')) {
        const fileNamePart = file.split('-').slice(1).join('-').substring(2);
        // console.log(`🔍 DEBUG: Comparing "${fileNamePart}" === "${fileName}"`);

        // Exact match first
        if (fileNamePart === fileName) {
          return true;
        }

        // Fuzzy match for common character substitutions
        const normalizedFileName = fileName
          .replace(/&/g, '-') // & to -
          .replace(/–/g, '-') // em dash to -
          .replace(/—/g, '-') // en dash to -
          .replace(/\s+/g, ' ') // multiple spaces to single space
          .trim();

        const normalizedFileNamePart = fileNamePart
          .replace(/&/g, '-') // & to -
          .replace(/–/g, '-') // em dash to -
          .replace(/—/g, '-') // en dash to -
          .replace(/\s+/g, ' ') // multiple spaces to single space
          .trim();

        // console.log(
        //   `🔍 DEBUG: Fuzzy comparing "${normalizedFileNamePart}" === "${normalizedFileName}"`
        // );

        if (normalizedFileNamePart === normalizedFileName) {
          console.log(`✓ Found fuzzy match!`);
          return true;
        }

        // Even more flexible match - check if they're 95% similar
        const similarity = calculateSimilarity(normalizedFileNamePart, normalizedFileName);
        // console.log(`🔍 DEBUG: Similarity score: ${similarity}`);

        if (similarity > 0.95) {
          console.log(`✓ Found high similarity match!`);
          return true;
        }
      }

      // Also check for exact matches
      const exactMatch = file === fileName || file === originalFileName;
      if (exactMatch) {
        console.log(`🔍 DEBUG: Found exact match: ${file}`);
      }
      return exactMatch;
    });

    if (matchingFile) {
      const localPath = path.join(summariesDir, matchingFile);
      console.log(`✓ Found timestamped file locally: ${localPath}`);
      const fileBuffer = await fs.readFile(localPath);
      return { buffer: fileBuffer, source: 'local', path: localPath };
    }
    console.log(`❌ No matching files found in summaries directory`);
  } catch (error) {
    console.error(`❌ Error reading summaries directory:`, error);
  }

  // Try alternative S3 paths including timestamped versions
  const s3Paths = [
    `summaries/${fileName}`,
    `summaries/${originalFileName}`,
    `temp/${fileName}`,
    `temp/${originalFileName}`,
    `uploads/${fileName}`,
    `uploads/${originalFileName}`,
  ];

  for (const s3Path of s3Paths) {
    try {
      await s3.send(
        new HeadObjectCommand({
          Bucket: process.env.AWS_FILE_BUCKET!,
          Key: s3Path,
        })
      );
      console.log(`✓ Found file in S3: ${s3Path}`);

      const fileStream = await getFileStream(s3Path, s3, process.env.AWS_FILE_BUCKET!);
      const chunks: Buffer[] = [];
      for await (const chunk of fileStream) {
        chunks.push(chunk as Buffer);
      }
      const fileBuffer = Buffer.concat(chunks);
      return { buffer: fileBuffer, source: 's3', path: s3Path };
    } catch (error) {
      // File doesn't exist at this S3 path, continue
    }
  }

  console.log(`❌ File not found in any location`);
  return null;
}

async function preCheckFiles(): Promise<void> {
  console.log('\n=== PRE-CHECK: Scanning summaries directory ===');

  try {
    const summariesDir = path.join(__dirname, '../../../../summaries');
    const files = await fs.readdir(summariesDir);

    console.log(`Found ${files.length} files in summaries directory:`);

    // Group files by pattern
    const timestampedFiles = files.filter((f) => f.includes('-0_'));
    const otherFiles = files.filter((f) => !f.includes('-0_'));

    console.log(`\nTimestamped files (${timestampedFiles.length}):`);
    timestampedFiles.forEach((f) => console.log(`  ${f}`));

    console.log(`\nOther files (${otherFiles.length}):`);
    otherFiles.forEach((f) => console.log(`  ${f}`));

    // Test the filename extraction logic
    console.log(`\nTesting filename extraction:`);
    timestampedFiles.slice(0, 5).forEach((file) => {
      const extracted = file.split('-').slice(1).join('-').substring(2);
      console.log(`  "${file}" -> "${extracted}"`);
    });
  } catch (error) {
    console.error('Error in pre-check:', error);
  }

  console.log('=== END PRE-CHECK ===\n');
}

async function processProgramFiles(): Promise<void> {
  // await preCheckFiles();

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Get all programs that have files but no program_files record
    const [programs] = (await sequelize.query(
      `SELECT p.id, p.summary_file 
       FROM programs p 
       LEFT JOIN program_files pf ON p.id = pf.program_id 
       WHERE p.summary_file IS NOT NULL 
       AND pf.id IS NULL
       ORDER BY p.id DESC`
    )) as [Program[], unknown];

    console.log(`Found ${programs.length} programs to process`);

    // Process each program file
    for (const program of programs) {
      try {
        const originalFileName = program.summary_file;
        const [timestamp, ...fileNameParts] = originalFileName.split('-');
        const fileName = fileNameParts.join('-').substring(2); // Remove the "0_" prefix

        const fileKey = `programs/${originalFileName}`;
        const pdfFileKey = `programs/${fileName.replace(/\.[^/.]+$/, '')}.pdf`;

        console.log(`Processing ${fileName}...`);

        let fileBuffer: Buffer;
        let actualFileKey = fileKey;

        // First try the expected S3 location
        try {
          await s3.send(
            new HeadObjectCommand({
              Bucket: process.env.AWS_FILE_BUCKET!,
              Key: fileKey,
            })
          );
          console.log(`✓ File found at expected location: ${fileKey}`);

          const fileStream = await getFileStream(fileKey, s3, process.env.AWS_FILE_BUCKET!);
          const chunks: Buffer[] = [];
          for await (const chunk of fileStream) {
            chunks.push(chunk as Buffer);
          }
          fileBuffer = Buffer.concat(chunks);
        } catch (error: any) {
          // Check for file not found errors (both NoSuchKey and NotFound)
          if (
            error.name === 'NoSuchKey' ||
            error.Code === 'NoSuchKey' ||
            error.name === 'NotFound' ||
            error.Code === 'NotFound' ||
            error.$metadata?.httpStatusCode === 404
          ) {
            console.log(`⚠️  File not found at expected location, searching alternatives...`);

            // Try to find file from alternative sources
            const alternativeFile = await findFileFromAlternativeSources(originalFileName, program);

            if (alternativeFile) {
              fileBuffer = alternativeFile.buffer;
              console.log(`✓ Using file from ${alternativeFile.source}: ${alternativeFile.path}`);

              // If found locally or in alternative S3 location, upload to correct location
              if (alternativeFile.source === 'local' || alternativeFile.path !== fileKey) {
                console.log(`📤 Uploading file to correct S3 location: ${fileKey}`);
                await s3Upload(s3, process.env.AWS_FILE_BUCKET!, fileBuffer, fileKey);
                actualFileKey = fileKey;
              } else {
                actualFileKey = alternativeFile.path;
              }
            } else {
              console.log(`❌ File not found anywhere, skipping program ${program.id}`);
              continue;
            }
          } else {
            throw error;
          }
        }

        // Generate PDF and file summary
        const pdfBuffer = await convertToPDF(fileBuffer);
        const fileSummary = await generateFileSummary(fileBuffer, fileName);

        // Upload PDF to S3
        await s3Upload(s3, process.env.AWS_FILE_BUCKET!, pdfBuffer, pdfFileKey);

        // Create program_files record
        await sequelize.query(
          `INSERT INTO program_files (
            program_id, name, file_url, pdf_name, pdf_url, 
            type, size, enabled, file_summary, "createdAt", "updatedAt"
          ) VALUES (
            :programId, :name, :fileUrl, :pdfName, :pdfUrl,
            :type, :size, :enabled, :fileSummary, NOW(), NOW()
          )`,
          {
            replacements: {
              programId: program.id,
              name: fileName,
              fileUrl: `https://${process.env.AWS_FILE_BUCKET}.s3.amazonaws.com/${actualFileKey}`,
              pdfName: fileName.replace(/\.[^/.]+$/, '.pdf'),
              pdfUrl: `https://${process.env.AWS_FILE_BUCKET}.s3.amazonaws.com/${pdfFileKey}`,
              type: fileName.split('.').pop()?.toLowerCase() || 'unknown',
              size: fileBuffer.length,
              enabled: true,
              fileSummary,
            },
          }
        );

        console.log(`✓ Successfully processed: ${fileName}`);
      } catch (error) {
        console.error(`Error processing file for program ${program.id}:`, error);
      }
    }

    console.log(`\nCompleted processing ${programs.length} program files`);
    await sequelize.close();
    process.exit(0);
  } catch (error) {
    console.error('Fatal error:', error);
    if (sequelize) {
      await sequelize.close();
    }
    process.exit(1);
  }
}

// Helper function to calculate string similarity
function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) {
    return 1.0;
  }

  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

// Helper function to calculate Levenshtein distance
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
}

processProgramFiles();

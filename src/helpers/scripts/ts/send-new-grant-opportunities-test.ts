// ts-node src/helpers/scripts/ts/send-new-grant-opportunities-test.ts

import dotenv from 'dotenv';
import dayjs from 'dayjs';
import path from 'path';
import fs from 'fs';
import { parseHTMLTemplate, sendEmailLetter } from '../../mail.helper';
import db from '../../../models';

dotenv.config();

const { Program, Client, Employee } = db;

// Function to get recent programs from the database
async function getRecentPrograms(days = 14) {
  const startDate = dayjs().subtract(days, 'day').format('YYYY-MM-DD');

  try {
    const programs = await Program.findAll({
      where: {
        createdAt: {
          [db.Sequelize.Op.gte]: startDate,
        },
        // Add any other criteria you need, e.g., showForFlexClient: true
      },
      order: [['createdAt', 'DESC']],
      limit: 10, // Limit to 10 most recent programs
    });

    console.log(`Found ${programs.length} programs created since ${startDate}`);
    return programs;
  } catch (error) {
    console.error('Error fetching programs:', error);
    return [];
  }
}

// Function to get a specific client
async function getTestClient() {
  try {
    // Find a specific client - modify this query as needed
    const client = await Client.findOne({
      where: {
        enabled: true,
      },
      include: [
        {
          model: Employee,
          as: 'primaryUser',
        },
      ],
    });

    if (!client) {
      console.error('No client found');
      return null;
    }

    console.log(
      `Found client: ${client.name} with primary user: ${
        client.primaryUser?.email || 'No primary user'
      }`
    );
    return client;
  } catch (error) {
    console.error('Error fetching client:', error);
    return null;
  }
}

// Format program data for the email template
function formatProgramsForEmail(programs: any[]): {
  programName: string;
  fileName: string;
  grantSummary: string;
  category: any;
  states: any;
  programLink: string;
}[] {
  return programs.map((program: any) => ({
    programName: program.name,
    fileName: program.summaryFile
      ? program.summaryFile.split('_').slice(1).join('')
      : 'No file available',
    grantSummary: program.description || 'No description available',
    category: program.category,
    states: program.states || 'All States',
    programLink: `${process.env.PUBLIC_URL}/programs/${program.id}`,
  }));
}

async function sendTestEmail(): Promise<void> {
  try {
    // Get recent programs from the database
    const recentPrograms = await getRecentPrograms();
    if (recentPrograms.length === 0) {
      console.log('No recent programs found. Using mock data instead.');
      return;
    }

    // Get a test client
    const testClient = await getTestClient();
    const recipientEmail = testClient?.primaryUser?.email || '<EMAIL>';

    // Format the programs for the email template
    const programDetails = formatProgramsForEmail(recentPrograms);

    // Correct path to the email template
    const templatePath = path.join(
      __dirname,
      '../../../email_templates/new-grant-opportunity.html'
    );

    // Check if the file exists
    if (!fs.existsSync(templatePath)) {
      console.error(`Template file not found at: ${templatePath}`);
      console.log('Current directory:', __dirname);
      console.log(
        'Available files in email_templates:',
        fs.existsSync(path.join(__dirname, '../../../email_templates'))
          ? fs.readdirSync(path.join(__dirname, '../../../email_templates'))
          : 'email_templates directory not found'
      );
      return;
    }

    const htmlContent = parseHTMLTemplate(templatePath, {
      programs: programDetails,
      viewMoreLink: `${process.env.PUBLIC_URL}/`,
    });

    console.log('SERVICE_MAILER_EMAIL:', process.env.SERVICE_MAILER_EMAIL);

    // Send the test email
    await sendEmailLetter({
      from: process.env.SERVICE_MAILER_EMAIL || '',
      to: recipientEmail,
      subject: 'New Grant Opportunities Available - TEST',
      plainver:
        'New grant opportunities have been posted. Please check your GrantTrack portal for details.',
      htmlver: htmlContent,
    });

    console.log(`Test email sent successfully to ${recipientEmail}`);
  } catch (error) {
    console.error('Error sending test email:', error);
  }
}

sendTestEmail();

-- CSV Award Import SQL Script
-- This script imports awards from CSV data with automatic client creation
-- 
-- CSV Format Expected:
-- <PERSON><PERSON>,Funder,Grant Program Name,Department,Start Date,End Date,Total Award Amount,Balance,Expended
--
-- Usage:
-- 1. Replace the VALUES section with your CSV data
-- 2. Update the @default_user_id variable with a valid employee ID
-- 3. Run this script in your database

-- Set default user ID for award assignments (change this to a valid employee ID)
SET @default_user_id = 1;

-- Create temporary table for CSV data (updated for Linden awards format)
CREATE TEMPORARY TABLE temp_csv_awards (
    client_name VARCHAR(255),
    funder VARCHAR(255),
    grant_program_name VARCHAR(500),
    department VARCHAR(255),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    total_award_amount VARCHAR(50),
    balance VARCHAR(50),
    expended VARCHAR(50),
    col10 VARCHAR(50),  -- Extra columns in your CSV
    col11 VARCHAR(50)
);

-- Insert Linden CSV data
INSERT INTO temp_csv_awards VALUES
('Linden', 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '9/1/2023', '6/30/2024', '$263,000.00', '', '$263,000.00', '', ''),
('Linden', 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '9/1/2024', '6/30/2025', '$214,000.00', '$214,000.00', '$0.00', '', ''),
('Linden', 'NJ Department of Environmental Protection', 'Stormwater Assistance Grants Program FY23', 'Engineering', '7/1/2023', '1/1/2026', '$25,000.00', '$25,000.00', '$0.00', '', ''),
('Linden', 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', '', '', '$335,000.00', '$539.43', '$334,460.57', '', ''),
('Linden', 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', '8/27/2018', 'N/A', '$1,250,000.00', '$515,202.96', '$734,797.04', '', ''),
('Linden', 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY20', 'Engineering', '7/22/2020', '7/22/2022', '$2,302,443.00', '$1,484,180.00', '$818,263.00', '', ''),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY20', 'Engineering', '11/1/2019', '11/1/2021', '$405,000.00', '$1,200,182.61', '($795,182.61)', '', ''),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY22', 'Engineering', '11/1/2021', '11/1/2024', '$440,000.00', '$1,723,687.74', '($1,283,687.74)', '', ''),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY23', 'Engineering', '11/1/2022', '11/1/2025', '$379,110.00', '$94,777.50', '$284,332.50', '', ''),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY24', 'Engineering', '11/1/2023', '11/1/2026', '$485,000.00', '$485,000.00', '$0.00', '', ''),
('Linden', 'Phillips 66', 'Phillips 66 Blue Cares Donation', 'Engineering', 'N/A', 'N/A', '$15,000.00', '$15,000.00', '$0.00', '', ''),
('Linden', 'Phillips 66', 'Phillips 66 Quiet Zone Donation', 'Engineering', 'N/A', 'N/A', '$30,000.00', '$30,000.00', '$0.00', '', ''),
('Linden', 'Union County', 'Community Development Block Grants (CDBG) FY23', 'Engineering', '9/1/2022', '8/30/2023', '$310,000.00', '$4,626.07', '$305,373.93', '', ''),
('Linden', 'Union County', 'Community Development Block Grants (CDBG) Program FY22', 'Engineering', '9/1/2021', '8/30/2022', '$370,000.00', '', '$370,000.00', '', ''),
('Linden', 'Union County', 'Community Development Block Grants (CDBG) Program FY24', 'Engineering', '9/1/2023', '8/30/2024', '$310,000.00', '$310,000.00', '$0.00', '', ''),
('Linden', 'Union County', 'Infrastructure and Municipal Aid Grant FY22', 'Engineering', '1/1/2022', '12/31/2022', '$90,000.00', '', '$90,000.00', '', ''),
('Linden', 'Union County', 'Infrastructure and Municipal Aid Grant FY24', 'Engineering', '1/1/2024', '12/31/2024', '$95,000.00', '$95,000.00', '$0.00', '', ''),
('Linden', 'Enbridge Inc.', 'Fueling Futures Program', 'Fire', 'WAITING FOR DEPT RESPONSE', '', '$8,565.00', '$77.50', '$8,487.50', '', ''),
('Linden', 'NJ Department of Community Affairs', 'Legislative Grant FY24', 'Fire', 'WAITING FOR DEPT RESPONSE', '', '$2,000,000.00', '$1,805,000.00', '$195,000.00', '', ''),
('Linden', 'Phillips 66', 'Phillips 66 FD Donation', 'Fire', 'N/A', 'N/A', '$20,000.00', '$22.25', '$19,977.75', '', '');

-- Function to parse currency (removes $ and commas, converts to decimal)
DELIMITER //
CREATE FUNCTION parse_currency(currency_str VARCHAR(50))
RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result DECIMAL(15,2);
    SET result = CAST(REPLACE(REPLACE(currency_str, '$', ''), ',', '') AS DECIMAL(15,2));
    RETURN result;
END//
DELIMITER ;

-- Function to parse date (handles MM/DD/YYYY format)
DELIMITER //
CREATE FUNCTION parse_date(date_str VARCHAR(50))
RETURNS DATE
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result DATE;
    -- Try MM/DD/YYYY format first
    IF date_str REGEXP '^[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}$' THEN
        SET result = STR_TO_DATE(date_str, '%m/%d/%Y');
    -- Try YYYY-MM-DD format
    ELSEIF date_str REGEXP '^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$' THEN
        SET result = STR_TO_DATE(date_str, '%Y-%m-%d');
    -- Try MM-DD-YYYY format
    ELSEIF date_str REGEXP '^[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}$' THEN
        SET result = STR_TO_DATE(date_str, '%m-%d-%Y');
    ELSE
        SET result = NULL;
    END IF;
    RETURN result;
END//
DELIMITER ;

-- Create or find clients
INSERT INTO clients (name, client_type, awards_enabled, can_create_award, award_notifications_enabled, billing_type, enabled, created_at, updated_at)
SELECT DISTINCT 
    csv.client_name,
    'Government',
    true,
    true,
    true,
    'Hourly',
    true,
    NOW(),
    NOW()
FROM temp_csv_awards csv
LEFT JOIN clients c ON c.name = csv.client_name
WHERE c.id IS NULL;

-- Insert awards
INSERT INTO awards (
    client_id,
    assignee_id,
    funder,
    grant_program_name,
    department,
    starts_on,
    ends_on,
    award_amount,
    award_balance,
    award_expended,
    status,
    source,
    category,
    application_required,
    award_letter_required,
    resolution_required,
    initial_agreement_required,
    contract_material_required,
    match_amount,
    match_expended,
    match_balance,
    enabled,
    created_at,
    updated_at
)
SELECT 
    c.id as client_id,
    @default_user_id as assignee_id,
    csv.funder,
    csv.grant_program_name,
    csv.department,
    parse_date(csv.start_date) as starts_on,
    parse_date(csv.end_date) as ends_on,
    parse_currency(csv.total_award_amount) as award_amount,
    parse_currency(csv.balance) as award_balance,
    parse_currency(csv.expended) as award_expended,
    'applicationRequired' as status,
    0 as source, -- Federal
    0 as category, -- Environmental
    true as application_required,
    true as award_letter_required,
    true as resolution_required,
    true as initial_agreement_required,
    true as contract_material_required,
    0 as match_amount,
    0 as match_expended,
    0 as match_balance,
    true as enabled,
    NOW() as created_at,
    NOW() as updated_at
FROM temp_csv_awards csv
JOIN clients c ON c.name = csv.client_name;

-- Create budget entries for each award (using common budget categories)
INSERT INTO award_budget_entries (
    award_id,
    user_id,
    name,
    award_amount,
    award_expended,
    award_balance,
    match_amount,
    match_expended,
    match_balance,
    enabled,
    created_at,
    updated_at
)
SELECT 
    a.id as award_id,
    @default_user_id as user_id,
    budget_category.name,
    0 as award_amount,
    0 as award_expended,
    0 as award_balance,
    0 as match_amount,
    0 as match_expended,
    0 as match_balance,
    true as enabled,
    NOW() as created_at,
    NOW() as updated_at
FROM awards a
CROSS JOIN (
    SELECT 'Personnel' as name UNION ALL
    SELECT 'Fringe Benefits' UNION ALL
    SELECT 'Travel' UNION ALL
    SELECT 'Equipment' UNION ALL
    SELECT 'Supplies' UNION ALL
    SELECT 'Contractual' UNION ALL
    SELECT 'Construction' UNION ALL
    SELECT 'Other' UNION ALL
    SELECT 'Total Direct Charges' UNION ALL
    SELECT 'Indirect Charges'
) budget_category
WHERE a.id IN (
    SELECT DISTINCT a2.id 
    FROM awards a2
    JOIN clients c ON c.id = a2.client_id
    JOIN temp_csv_awards csv ON csv.client_name = c.name
);

-- Clean up temporary table and functions
DROP TEMPORARY TABLE temp_csv_awards;
DROP FUNCTION parse_currency;
DROP FUNCTION parse_date;

-- Display import summary
SELECT 
    'Import Summary' as summary,
    COUNT(*) as total_awards_created
FROM awards 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

SELECT 
    'Clients Created/Used' as summary,
    c.name as client_name,
    COUNT(a.id) as awards_count
FROM clients c
JOIN awards a ON a.client_id = c.id
WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
GROUP BY c.id, c.name
ORDER BY c.name;

-- Verify the import
SELECT 
    a.id,
    c.name as client_name,
    a.funder,
    a.grant_program_name,
    a.department,
    a.starts_on,
    a.ends_on,
    a.award_amount,
    a.award_balance,
    a.award_expended,
    a.status
FROM awards a
JOIN clients c ON c.id = a.client_id
WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
ORDER BY a.id;

COMMIT;

-- CSV Award Import SQL Script
-- This script imports awards from CSV data with automatic client creation
-- 
-- CSV Format Expected:
-- <PERSON><PERSON>,Funder,Grant Program Name,Department,Start Date,End Date,Total Award Amount,Balance,Expended
--
-- Usage:
-- 1. Replace the VALUES section with your CSV data
-- 2. Update the @default_user_id variable with a valid employee ID
-- 3. Run this script in your database

-- Set default user ID for award assignments (change this to a valid employee ID)
SET @default_user_id = 1;

-- Create temporary table for CSV data
CREATE TEMPORARY TABLE temp_csv_awards (
    client_name VARCHAR(255),
    funder VARCHAR(255),
    grant_program_name VARCHAR(255),
    department VARCHAR(255),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    total_award_amount VARCHAR(50),
    balance VARCHAR(50),
    expended VARCHAR(50)
);

-- Insert CSV data (replace this section with your actual CSV data)
INSERT INTO temp_csv_awards VALUES
('City of Springfield', 'EPA', 'Clean Water Initiative', 'Public Works', '01/15/2024', '12/31/2025', '$150,000', '$120,000', '$30,000'),
('County of Madison', 'DOT', 'Highway Safety Program', 'Transportation', '03/01/2024', '02/28/2026', '$75,000', '$75,000', '$0'),
('Springfield School District', 'DOE', 'STEM Education Grant', 'Education', '09/01/2024', '08/31/2025', '$50,000', '$45,000', '$5,000'),
('Metro Health Department', 'CDC', 'Public Health Emergency Preparedness', 'Health Services', '10/01/2023', '09/30/2024', '$200,000', '$50,000', '$150,000'),
('Parks and Recreation District', 'National Park Service', 'Community Recreation Enhancement', 'Parks & Recreation', '04/01/2024', '03/31/2026', '$85,000', '$85,000', '$0');

-- Function to parse currency (removes $ and commas, converts to decimal)
DELIMITER //
CREATE FUNCTION parse_currency(currency_str VARCHAR(50))
RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result DECIMAL(15,2);
    SET result = CAST(REPLACE(REPLACE(currency_str, '$', ''), ',', '') AS DECIMAL(15,2));
    RETURN result;
END//
DELIMITER ;

-- Function to parse date (handles MM/DD/YYYY format)
DELIMITER //
CREATE FUNCTION parse_date(date_str VARCHAR(50))
RETURNS DATE
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result DATE;
    -- Try MM/DD/YYYY format first
    IF date_str REGEXP '^[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}$' THEN
        SET result = STR_TO_DATE(date_str, '%m/%d/%Y');
    -- Try YYYY-MM-DD format
    ELSEIF date_str REGEXP '^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$' THEN
        SET result = STR_TO_DATE(date_str, '%Y-%m-%d');
    -- Try MM-DD-YYYY format
    ELSEIF date_str REGEXP '^[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}$' THEN
        SET result = STR_TO_DATE(date_str, '%m-%d-%Y');
    ELSE
        SET result = NULL;
    END IF;
    RETURN result;
END//
DELIMITER ;

-- Create or find clients
INSERT INTO clients (name, client_type, awards_enabled, can_create_award, award_notifications_enabled, billing_type, enabled, created_at, updated_at)
SELECT DISTINCT 
    csv.client_name,
    'Government',
    true,
    true,
    true,
    'Hourly',
    true,
    NOW(),
    NOW()
FROM temp_csv_awards csv
LEFT JOIN clients c ON c.name = csv.client_name
WHERE c.id IS NULL;

-- Insert awards
INSERT INTO awards (
    client_id,
    assignee_id,
    funder,
    grant_program_name,
    department,
    starts_on,
    ends_on,
    award_amount,
    award_balance,
    award_expended,
    status,
    source,
    category,
    application_required,
    award_letter_required,
    resolution_required,
    initial_agreement_required,
    contract_material_required,
    match_amount,
    match_expended,
    match_balance,
    enabled,
    created_at,
    updated_at
)
SELECT 
    c.id as client_id,
    @default_user_id as assignee_id,
    csv.funder,
    csv.grant_program_name,
    csv.department,
    parse_date(csv.start_date) as starts_on,
    parse_date(csv.end_date) as ends_on,
    parse_currency(csv.total_award_amount) as award_amount,
    parse_currency(csv.balance) as award_balance,
    parse_currency(csv.expended) as award_expended,
    'applicationRequired' as status,
    0 as source, -- Federal
    0 as category, -- Environmental
    true as application_required,
    true as award_letter_required,
    true as resolution_required,
    true as initial_agreement_required,
    true as contract_material_required,
    0 as match_amount,
    0 as match_expended,
    0 as match_balance,
    true as enabled,
    NOW() as created_at,
    NOW() as updated_at
FROM temp_csv_awards csv
JOIN clients c ON c.name = csv.client_name;

-- Create budget entries for each award (using common budget categories)
INSERT INTO award_budget_entries (
    award_id,
    user_id,
    name,
    award_amount,
    award_expended,
    award_balance,
    match_amount,
    match_expended,
    match_balance,
    enabled,
    created_at,
    updated_at
)
SELECT 
    a.id as award_id,
    @default_user_id as user_id,
    budget_category.name,
    0 as award_amount,
    0 as award_expended,
    0 as award_balance,
    0 as match_amount,
    0 as match_expended,
    0 as match_balance,
    true as enabled,
    NOW() as created_at,
    NOW() as updated_at
FROM awards a
CROSS JOIN (
    SELECT 'Personnel' as name UNION ALL
    SELECT 'Fringe Benefits' UNION ALL
    SELECT 'Travel' UNION ALL
    SELECT 'Equipment' UNION ALL
    SELECT 'Supplies' UNION ALL
    SELECT 'Contractual' UNION ALL
    SELECT 'Construction' UNION ALL
    SELECT 'Other' UNION ALL
    SELECT 'Total Direct Charges' UNION ALL
    SELECT 'Indirect Charges'
) budget_category
WHERE a.id IN (
    SELECT DISTINCT a2.id 
    FROM awards a2
    JOIN clients c ON c.id = a2.client_id
    JOIN temp_csv_awards csv ON csv.client_name = c.name
);

-- Clean up temporary table and functions
DROP TEMPORARY TABLE temp_csv_awards;
DROP FUNCTION parse_currency;
DROP FUNCTION parse_date;

-- Display import summary
SELECT 
    'Import Summary' as summary,
    COUNT(*) as total_awards_created
FROM awards 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

SELECT 
    'Clients Created/Used' as summary,
    c.name as client_name,
    COUNT(a.id) as awards_count
FROM clients c
JOIN awards a ON a.client_id = c.id
WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
GROUP BY c.id, c.name
ORDER BY c.name;

-- Verify the import
SELECT 
    a.id,
    c.name as client_name,
    a.funder,
    a.grant_program_name,
    a.department,
    a.starts_on,
    a.ends_on,
    a.award_amount,
    a.award_balance,
    a.award_expended,
    a.status
FROM awards a
JOIN clients c ON c.id = a.client_id
WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
ORDER BY a.id;

COMMIT;

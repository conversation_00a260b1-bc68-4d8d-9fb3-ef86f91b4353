-- Linden Awards Import SQL Script
-- This script imports the Linden awards data with automatic client creation
-- 
-- Usage:
-- 1. Update the @default_user_id variable with a valid employee ID
-- 2. Run this script in your MySQL database

-- Set default user ID for award assignments (change this to a valid employee ID)
SET @default_user_id = 1;

-- Create temporary table for Linden CSV data
CREATE TEMPORARY TABLE temp_linden_awards (
    client_name VARCHAR(255),
    funder VARCHAR(255),
    grant_program_name VARCHAR(500),
    department VARCHAR(255),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    total_award_amount VARCHAR(50),
    balance VARCHAR(50),
    expended VARCHAR(50)
);

-- Insert all Linden awards data
INSERT INTO temp_linden_awards VALUES
('Linden', 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '9/1/2023', '6/30/2024', '$263,000.00', '', '$263,000.00'),
('Linden', 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '9/1/2024', '6/30/2025', '$214,000.00', '$214,000.00', '$0.00'),
('Linden', 'NJ Department of Environmental Protection', 'Stormwater Assistance Grants Program FY23', 'Engineering', '7/1/2023', '1/1/2026', '$25,000.00', '$25,000.00', '$0.00'),
('Linden', 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', '', '', '$335,000.00', '$539.43', '$334,460.57'),
('Linden', 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', '8/27/2018', 'N/A', '$1,250,000.00', '$515,202.96', '$734,797.04'),
('Linden', 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY20', 'Engineering', '7/22/2020', '7/22/2022', '$2,302,443.00', '$1,484,180.00', '$818,263.00'),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY20', 'Engineering', '11/1/2019', '11/1/2021', '$405,000.00', '$1,200,182.61', '($795,182.61)'),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY22', 'Engineering', '11/1/2021', '11/1/2024', '$440,000.00', '$1,723,687.74', '($1,283,687.74)'),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY23', 'Engineering', '11/1/2022', '11/1/2025', '$379,110.00', '$94,777.50', '$284,332.50'),
('Linden', 'NJ Department of Transportation', 'Municipal Aid Grant Program FY24', 'Engineering', '11/1/2023', '11/1/2026', '$485,000.00', '$485,000.00', '$0.00'),
('Linden', 'Phillips 66', 'Phillips 66 Blue Cares Donation', 'Engineering', 'N/A', 'N/A', '$15,000.00', '$15,000.00', '$0.00'),
('Linden', 'Phillips 66', 'Phillips 66 Quiet Zone Donation', 'Engineering', 'N/A', 'N/A', '$30,000.00', '$30,000.00', '$0.00'),
('Linden', 'Union County', 'Community Development Block Grants (CDBG) FY23', 'Engineering', '9/1/2022', '8/30/2023', '$310,000.00', '$4,626.07', '$305,373.93'),
('Linden', 'Union County', 'Community Development Block Grants (CDBG) Program FY22', 'Engineering', '9/1/2021', '8/30/2022', '$370,000.00', '', '$370,000.00'),
('Linden', 'Union County', 'Community Development Block Grants (CDBG) Program FY24', 'Engineering', '9/1/2023', '8/30/2024', '$310,000.00', '$310,000.00', '$0.00'),
('Linden', 'Union County', 'Infrastructure and Municipal Aid Grant FY22', 'Engineering', '1/1/2022', '12/31/2022', '$90,000.00', '', '$90,000.00'),
('Linden', 'Union County', 'Infrastructure and Municipal Aid Grant FY24', 'Engineering', '1/1/2024', '12/31/2024', '$95,000.00', '$95,000.00', '$0.00'),
('Linden', 'Enbridge Inc.', 'Fueling Futures Program', 'Fire', 'WAITING FOR DEPT RESPONSE', '', '$8,565.00', '$77.50', '$8,487.50'),
('Linden', 'NJ Department of Community Affairs', 'Legislative Grant FY24', 'Fire', 'WAITING FOR DEPT RESPONSE', '', '$2,000,000.00', '$1,805,000.00', '$195,000.00'),
('Linden', 'Phillips 66', 'Phillips 66 FD Donation', 'Fire', 'N/A', 'N/A', '$20,000.00', '$22.25', '$19,977.75'),
('Linden', 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY18', 'Health', '11/15/2018', '11/15/2022', '$4,000.00', '$789.55', '$3,210.45'),
('Linden', 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY23', 'Health', '11/15/2023', '11/15/2027', '$12,000.00', '$2,264.00', '$9,736.00'),
('Linden', 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY24', 'Health', '11/15/2024', '11/15/2028', '$30,160.00', '$185.00', '$29,975.00'),
('Linden', 'NJ Association of County and City Health Officials', 'Enhancing Local Public Health Infrastructure Grants Program FY23', 'Health', '7/1/2023', '6/30/2024', '$599,677.00', '$58,835.22', '$540,841.78'),
('Linden', 'NJ Department of Health', 'COVID-19 Vaccination Supplemental Funding Grants Program FY22/23', 'Health', '7/1/2022', '6/30/2023', '$135,000.00', '', '$135,000.00'),
('Linden', 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY23', 'Health', '7/1/2023', '6/30/2024', '$274,735.00', '$162.00', '$274,573.00'),
('Linden', 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY24', 'Health', '7/1/2024', '6/30/2025', '$406,046.00', '$116,694.42', '$289,351.58'),
('Linden', 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY25', 'Health', '7/1/2025', '6/30/2026', '$74,664.00', '$35,801.07', '$38,862.93'),
('Linden', 'National Opioid Settlement', 'Allergan Settlement', 'Mayor''s Office', '4/30/2024', 'N/A', '$19,832.37', '', '$19,832.37'),
('Linden', 'National Opioid Settlement', 'CVS Settlement', 'Mayor''s Office', '4/30/2024', 'N/A', '$19,106.35', '', '$19,106.35'),
('Linden', 'National Opioid Settlement', 'J&J Settlement', 'Mayor''s Office', '12/30/2024', 'N/A', '$31,270.55', '$6,027.74', '$25,242.81'),
('Linden', 'National Opioid Settlement', 'Janssen Settlement', 'Mayor''s Office', '5/19/2023', 'N/A', '$17,113.37', '$3,153.35', '$13,960.02'),
('Linden', 'National Opioid Settlement', 'Janssen Settlement', 'Mayor''s Office', '6/20/2024', 'N/A', '$27,133.01', '', '$27,133.01'),
('Linden', 'National Opioid Settlement', 'McKinsey Settlement', 'Mayor''s Office', '11/9/2023', 'N/A', '$6,187.31', '', '$6,187.31'),
('Linden', 'National Opioid Settlement', 'McKinsey Settlement', 'Mayor''s Office', '4/30/2024', 'N/A', '$12,499.84', '', '$12,499.84'),
('Linden', 'National Opioid Settlement', 'Opioid Settlement', 'Mayor''s Office', '4/30/2024', 'N/A', '$46,877.00', '', '$46,877.00'),
('Linden', 'National Opioid Settlement', 'Opioid Settlement FY22', 'Mayor''s Office', '8/4/2022', 'N/A', '$51,275.67', '$818.91', '$50,456.76'),
('Linden', 'National Opioid Settlement', 'Teva Settlement', 'Mayor''s Office', '4/30/2024', 'N/A', '$18,578.98', '', '$18,578.98'),
('Linden', 'National Opioid Settlement', 'Walgreens Settlement', 'Mayor''s Office', '4/30/2024', 'N/A', '$21,814.03', '', '$21,814.03'),
('Linden', 'National Opioid Settlement', 'Walmart Settlement', 'Mayor''s Office', '4/30/2024', 'N/A', '$86,428.08', '', '$86,428.08'),
('Linden', 'National Opioid Settlement', 'Abatement Settlement', 'Mayor''s Office', '5/19/2023', 'N/A', '$5,259.21', '', '$5,259.21'),
('Linden', 'National Opioid Settlement', 'Distributor Settlement', 'Mayor''s Office', '8/21/2023', 'N/A', '$24,289.55', '', '$24,289.55'),
('Linden', 'National Opioid Settlement', 'Endo Public Settlement', 'Mayor''s Office', '10/10/2024', 'N/A', '$10,828.32', '', '$10,828.32'),
('Linden', 'City of Linden', 'Capital Funding Police Allocation', 'Police', 'N/A', 'N/A', '$350,000.00', '$114,267.10', '$235,732.90'),
('Linden', 'City of Linden', 'Capital Funding Police Allocation', 'Police', 'N/A', 'N/A', '$400,000.00', '$19,171.14', '$380,828.86'),
('Linden', 'NJ Department of Human Services', 'Alcohol Education, Rehabilitation and Enforcement Fund FY19', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$5,947.73', '$48.81', '$5,898.92'),
('Linden', 'NJ Department of Human Services', 'Alcohol Education, Rehabilitation and Enforcement Fund FY20', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$2,667.32', '$1,467.32', '$1,200.00'),
('Linden', 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY22', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$32,400.00', '', '$32,400.00'),
('Linden', 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY23', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$32,400.00', '', '$32,400.00'),
('Linden', 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY24', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$45,150.00', '', '$45,150.00'),
('Linden', 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY25', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$45,150.00', '', '$45,150.00'),
('Linden', 'NJ Department of Transportation', 'Safe Corridor Program FY22', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$57,217.00', '', '$57,217.00'),
('Linden', 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY20', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$8,970.82', '$97.62', '$8,873.20'),
('Linden', 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY21', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$5,656.46', '', '$5,656.46'),
('Linden', 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY22', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$8,074.13', '', '$8,074.13'),
('Linden', 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY23', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$9,650.30', '', '$9,650.30'),
('Linden', 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY25', 'Police', 'WAITING FOR DEPT RESPONSE', '', '$10,653.39', '', '$10,653.39'),
('Linden', 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY22', 'Police', '5/23/2022', '6/5/2022', '$7,000.00', '', '$7,000.00'),
('Linden', 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY23', 'Police', '5/22/2023', '6/4/2023', '$7,000.00', '', '$7,000.00'),
('Linden', 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY24', 'Police', '5/20/2024', '6/2/2024', '$8,750.00', '', '$8,750.00');

-- Function to parse currency (removes $ and commas, handles negative amounts in parentheses)
DELIMITER //
CREATE FUNCTION parse_currency(currency_str VARCHAR(50))
RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result DECIMAL(15,2);
    DECLARE clean_str VARCHAR(50);

    -- Handle empty or null values
    IF currency_str IS NULL OR TRIM(currency_str) = '' THEN
        RETURN 0.00;
    END IF;

    -- Handle negative amounts in parentheses like ($795,182.61)
    IF currency_str LIKE '(%' THEN
        SET clean_str = REPLACE(REPLACE(REPLACE(currency_str, '(', ''), ')', ''), '$', '');
        SET clean_str = REPLACE(clean_str, ',', '');
        SET result = -CAST(clean_str AS DECIMAL(15,2));
    ELSE
        -- Handle regular amounts
        SET clean_str = REPLACE(REPLACE(currency_str, '$', ''), ',', '');
        SET result = CAST(clean_str AS DECIMAL(15,2));
    END IF;

    RETURN result;
END//
DELIMITER ;

-- Function to parse date (handles various formats and special cases)
DELIMITER //
CREATE FUNCTION parse_date(date_str VARCHAR(50))
RETURNS DATE
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result DATE;

    -- Handle special cases
    IF date_str IS NULL OR TRIM(date_str) = '' OR date_str = 'N/A' OR date_str = 'WAITING FOR DEPT RESPONSE' THEN
        RETURN NULL;
    END IF;

    -- Try M/D/YYYY format first
    IF date_str REGEXP '^[0-9]{1,2}/[0-9]{1,2}/[0-9]{4}$' THEN
        SET result = STR_TO_DATE(date_str, '%m/%d/%Y');
    -- Try MM/DD/YYYY format
    ELSEIF date_str REGEXP '^[0-9]{2}/[0-9]{2}/[0-9]{4}$' THEN
        SET result = STR_TO_DATE(date_str, '%m/%d/%Y');
    -- Try YYYY-MM-DD format
    ELSEIF date_str REGEXP '^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$' THEN
        SET result = STR_TO_DATE(date_str, '%Y-%m-%d');
    -- Try MM-DD-YYYY format
    ELSEIF date_str REGEXP '^[0-9]{1,2}-[0-9]{1,2}-[0-9]{4}$' THEN
        SET result = STR_TO_DATE(date_str, '%m-%d-%Y');
    ELSE
        SET result = NULL;
    END IF;

    RETURN result;
END//
DELIMITER ;

-- Create or find the Linden client
INSERT INTO clients (name, client_type, awards_enabled, can_create_award, award_notifications_enabled, billing_type, enabled, created_at, updated_at)
SELECT DISTINCT
    'Linden',
    'Government',
    true,
    true,
    true,
    'Hourly',
    true,
    NOW(),
    NOW()
FROM temp_linden_awards csv
LEFT JOIN clients c ON c.name = 'Linden'
WHERE c.id IS NULL
LIMIT 1;

-- Insert awards with proper data parsing
INSERT INTO awards (
    client_id,
    assignee_id,
    funder,
    grant_program_name,
    department,
    starts_on,
    ends_on,
    award_amount,
    award_balance,
    award_expended,
    status,
    source,
    category,
    application_required,
    award_letter_required,
    resolution_required,
    initial_agreement_required,
    contract_material_required,
    match_amount,
    match_expended,
    match_balance,
    enabled,
    created_at,
    updated_at
)
SELECT
    c.id as client_id,
    @default_user_id as assignee_id,
    csv.funder,
    csv.grant_program_name,
    csv.department,
    parse_date(csv.start_date) as starts_on,
    parse_date(csv.end_date) as ends_on,
    parse_currency(csv.total_award_amount) as award_amount,
    parse_currency(csv.balance) as award_balance,
    parse_currency(csv.expended) as award_expended,
    'applicationRequired' as status,
    0 as source, -- Federal
    0 as category, -- Environmental
    true as application_required,
    true as award_letter_required,
    true as resolution_required,
    true as initial_agreement_required,
    true as contract_material_required,
    0 as match_amount,
    0 as match_expended,
    0 as match_balance,
    true as enabled,
    NOW() as created_at,
    NOW() as updated_at
FROM temp_linden_awards csv
JOIN clients c ON c.name = csv.client_name;

-- Create budget entries for each award
INSERT INTO award_budget_entries (
    award_id,
    user_id,
    name,
    award_amount,
    award_expended,
    award_balance,
    match_amount,
    match_expended,
    match_balance,
    enabled,
    created_at,
    updated_at
)
SELECT
    a.id as award_id,
    @default_user_id as user_id,
    budget_category.name,
    0 as award_amount,
    0 as award_expended,
    0 as award_balance,
    0 as match_amount,
    0 as match_expended,
    0 as match_balance,
    true as enabled,
    NOW() as created_at,
    NOW() as updated_at
FROM awards a
CROSS JOIN (
    SELECT 'Personnel' as name UNION ALL
    SELECT 'Fringe Benefits' UNION ALL
    SELECT 'Travel' UNION ALL
    SELECT 'Equipment' UNION ALL
    SELECT 'Supplies' UNION ALL
    SELECT 'Contractual' UNION ALL
    SELECT 'Construction' UNION ALL
    SELECT 'Other' UNION ALL
    SELECT 'Total Direct Charges' UNION ALL
    SELECT 'Indirect Charges'
) budget_category
WHERE a.id IN (
    SELECT DISTINCT a2.id
    FROM awards a2
    JOIN clients c ON c.id = a2.client_id
    WHERE c.name = 'Linden'
    AND a2.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
);

-- Clean up temporary table and functions
DROP TEMPORARY TABLE temp_linden_awards;
DROP FUNCTION IF EXISTS parse_currency;
DROP FUNCTION IF EXISTS parse_date;

-- Display import summary
SELECT
    'Import Summary' as summary,
    COUNT(*) as total_awards_created
FROM awards a
JOIN clients c ON c.id = a.client_id
WHERE c.name = 'Linden'
AND a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

-- Verify the import
SELECT
    a.id,
    c.name as client_name,
    a.funder,
    a.grant_program_name,
    a.department,
    a.starts_on,
    a.ends_on,
    a.award_amount,
    a.award_balance,
    a.award_expended,
    a.status
FROM awards a
JOIN clients c ON c.id = a.client_id
WHERE c.name = 'Linden'
AND a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
ORDER BY a.id;

COMMIT;

-- Simple Linden Awards Insert
-- Replace client_id value (1) with the actual Linden client ID from your database
-- Replace assignee_id value (1) with a valid employee ID

INSERT INTO awards (
    client_id,
    assignee_id,
    funder,
    grant_program_name,
    department,
    starts_on,
    ends_on,
    award_amount,
    award_balance,
    award_expended,
    status,
    source,
    category,
    "applicationRequired",
    "awardLetterRequired",
    "resolutionRequired",
    "initialAgreementRequired",
    "contractMaterialRequired",
    "reportRequired",
    match_amount,
    match_expended,
    match_balance,
    payments_requested,
    payments_received,
    is_approved,
    "isCompliance",
    enabled,
    "createdAt",
    "updatedAt"
) VALUES
(1, 1, 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '2023-09-01', '2024-06-30 23:59:59+00', 263000.00, 0.00, 263000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '2024-09-01', '2025-06-30 23:59:59+00', 214000.00, 214000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Environmental Protection', 'Stormwater Assistance Grants Program FY23', 'Engineering', '2023-07-01', '2026-01-01 23:59:59+00', 25000.00, 25000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', NULL, NULL, 335000.00, 539.43, 334460.57, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', '2018-08-27', NULL, 1250000.00, 515202.96, 734797.04, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY20', 'Engineering', '2020-07-22', '2022-07-22 23:59:59+00', 2302443.00, 1484180.00, 818263.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY20', 'Engineering', '2019-11-01', '2021-11-01 23:59:59+00', 405000.00, 1200182.61, -795182.61, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY22', 'Engineering', '2021-11-01', '2024-11-01 23:59:59+00', 440000.00, 1723687.74, -1283687.74, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY23', 'Engineering', '2022-11-01', '2025-11-01 23:59:59+00', 379110.00, 94777.50, 284332.50, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY24', 'Engineering', '2023-11-01', '2026-11-01 23:59:59+00', 485000.00, 485000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Phillips 66', 'Phillips 66 Blue Cares Donation', 'Engineering', NULL, NULL, 15000.00, 15000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Phillips 66', 'Phillips 66 Quiet Zone Donation', 'Engineering', NULL, NULL, 30000.00, 30000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Union County', 'Community Development Block Grants (CDBG) FY23', 'Engineering', '2022-09-01', '2023-08-30 23:59:59+00', 310000.00, 4626.07, 305373.93, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Union County', 'Community Development Block Grants (CDBG) Program FY22', 'Engineering', '2021-09-01', '2022-08-30 23:59:59+00', 370000.00, 0.00, 370000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Union County', 'Community Development Block Grants (CDBG) Program FY24', 'Engineering', '2023-09-01', '2024-08-30 23:59:59+00', 310000.00, 310000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Union County', 'Infrastructure and Municipal Aid Grant FY22', 'Engineering', '2022-01-01', '2022-12-31 23:59:59+00', 90000.00, 0.00, 90000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Union County', 'Infrastructure and Municipal Aid Grant FY24', 'Engineering', '2024-01-01', '2024-12-31 23:59:59+00', 95000.00, 95000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Enbridge Inc.', 'Fueling Futures Program', 'Fire', NULL, NULL, 8565.00, 77.50, 8487.50, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Community Affairs', 'Legislative Grant FY24', 'Fire', NULL, NULL, 2000000.00, 1805000.00, 195000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'Phillips 66', 'Phillips 66 FD Donation', 'Fire', NULL, NULL, 20000.00, 22.25, 19977.75, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY18', 'Health', '2018-11-15', '2022-11-15 23:59:59+00', 4000.00, 789.55, 3210.45, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY23', 'Health', '2023-11-15', '2027-11-15 23:59:59+00', 12000.00, 2264.00, 9736.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY24', 'Health', '2024-11-15', '2028-11-15 23:59:59+00', 30160.00, 185.00, 29975.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Association of County and City Health Officials', 'Enhancing Local Public Health Infrastructure Grants Program FY23', 'Health', '2023-07-01', '2024-06-30 23:59:59+00', 599677.00, 58835.22, 540841.78, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Health', 'COVID-19 Vaccination Supplemental Funding Grants Program FY22/23', 'Health', '2022-07-01', '2023-06-30 23:59:59+00', 135000.00, 0.00, 135000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY23', 'Health', '2023-07-01', '2024-06-30 23:59:59+00', 274735.00, 162.00, 274573.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY24', 'Health', '2024-07-01', '2025-06-30 23:59:59+00', 406046.00, 116694.42, 289351.58, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY25', 'Health', '2025-07-01', '2026-06-30 23:59:59+00', 74664.00, 35801.07, 38862.93, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Allergan Settlement', 'Mayor''s Office', '2024-04-30', NULL, 19832.37, 0.00, 19832.37, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'CVS Settlement', 'Mayor''s Office', '2024-04-30', NULL, 19106.35, 0.00, 19106.35, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'J&J Settlement', 'Mayor''s Office', '2024-12-30', NULL, 31270.55, 6027.74, 25242.81, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Janssen Settlement', 'Mayor''s Office', '2023-05-19', NULL, 17113.37, 3153.35, 13960.02, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Janssen Settlement', 'Mayor''s Office', '2024-06-20', NULL, 27133.01, 0.00, 27133.01, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'McKinsey Settlement', 'Mayor''s Office', '2023-11-09', NULL, 6187.31, 0.00, 6187.31, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'McKinsey Settlement', 'Mayor''s Office', '2024-04-30', NULL, 12499.84, 0.00, 12499.84, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Opioid Settlement', 'Mayor''s Office', '2024-04-30', NULL, 46877.00, 0.00, 46877.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Opioid Settlement FY22', 'Mayor''s Office', '2022-08-04', NULL, 51275.67, 818.91, 50456.76, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Teva Settlement', 'Mayor''s Office', '2024-04-30', NULL, 18578.98, 0.00, 18578.98, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Walgreens Settlement', 'Mayor''s Office', '2024-04-30', NULL, 21814.03, 0.00, 21814.03, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Walmart Settlement', 'Mayor''s Office', '2024-04-30', NULL, 86428.08, 0.00, 86428.08, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Abatement Settlement', 'Mayor''s Office', '2023-05-19', NULL, 5259.21, 0.00, 5259.21, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Distributor Settlement', 'Mayor''s Office', '2023-08-21', NULL, 24289.55, 0.00, 24289.55, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'National Opioid Settlement', 'Endo Public Settlement', 'Mayor''s Office', '2024-10-10', NULL, 10828.32, 0.00, 10828.32, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'City of Linden', 'Capital Funding Police Allocation', 'Police', NULL, NULL, 350000.00, 114267.10, 235732.90, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'City of Linden', 'Capital Funding Police Allocation', 'Police', NULL, NULL, 400000.00, 19171.14, 380828.86, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Human Services', 'Alcohol Education, Rehabilitation and Enforcement Fund FY19', 'Police', NULL, NULL, 5947.73, 48.81, 5898.92, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Human Services', 'Alcohol Education, Rehabilitation and Enforcement Fund FY20', 'Police', NULL, NULL, 2667.32, 1467.32, 1200.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY22', 'Police', NULL, NULL, 32400.00, 0.00, 32400.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY23', 'Police', NULL, NULL, 32400.00, 0.00, 32400.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY24', 'Police', NULL, NULL, 45150.00, 0.00, 45150.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY25', 'Police', NULL, NULL, 45150.00, 0.00, 45150.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Department of Transportation', 'Safe Corridor Program FY22', 'Police', NULL, NULL, 57217.00, 0.00, 57217.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY20', 'Police', NULL, NULL, 8970.82, 97.62, 8873.20, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY21', 'Police', NULL, NULL, 5656.46, 0.00, 5656.46, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY22', 'Police', NULL, NULL, 8074.13, 0.00, 8074.13, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY23', 'Police', NULL, NULL, 9650.30, 0.00, 9650.30, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY25', 'Police', NULL, NULL, 10653.39, 0.00, 10653.39, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY22', 'Police', '2022-05-23', '2022-06-05 23:59:59+00', 7000.00, 0.00, 7000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY23', 'Police', '2023-05-22', '2023-06-04 23:59:59+00', 7000.00, 0.00, 7000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
(1, 1, 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY24', 'Police', '2024-05-20', '2024-06-02 23:59:59+00', 8750.00, 0.00, 8750.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW());

# PDF Conversion Feature

## Overview

The PDF conversion feature automatically converts DOCX files to PDF format while preserving hyperlinks, including those without full protocols (e.g., "example.com" or "www.example.com"). This feature is integrated into the file upload process and ensures that all document links remain clickable in the generated PDF.

## Key Features

### Enhanced Link Preservation
- **Full Protocol Links**: `https://example.com`, `http://example.com`
- **Partial Domain Links**: `example.com`, `www.example.com`
- **Email Links**: `<EMAIL>`, `mailto:<EMAIL>`
- **FTP Links**: `ftp://files.example.com`
- **File Links**: `file:///path/to/file.txt`

### Automatic Processing
- Triggered automatically during file upload
- No manual intervention required
- Both original DOCX and converted PDF are stored

## Technical Implementation

### Core Function
Located in `src/helpers/file.helper.ts`:

<augment_code_snippet path="src/helpers/file.helper.ts" mode="EXCERPT">
````typescript
export async function convertToPDF(buffer: Buffer): Promise<Buffer> {
  try {
    // Configure PDF export options to preserve hyperlinks
    const pdfExportFilter = 'writer_pdf_Export:{"PDFViewSelection":{"type":"long","value":"0"},"ExportLinksRelativeFsys":{"type":"boolean","value":"false"},"ConvertOOoTargetToPDFTarget":{"type":"boolean","value":"true"},"ExportBookmarksToPDFDestination":{"type":"boolean","value":"true"}}';
    
    return await convertAsync(buffer, '.pdf', pdfExportFilter);
  } catch (error) {
    console.error('Error converting file to PDF:', error);
    throw error;
  }
}
````
</augment_code_snippet>

### LibreOffice PDF Export Parameters
The conversion uses specific LibreOffice parameters to ensure link preservation:

- **`PDFViewSelection`**: Controls PDF view settings
- **`ExportLinksRelativeFsys`**: Ensures relative links are preserved
- **`ConvertOOoTargetToPDFTarget`**: Converts internal targets to PDF targets
- **`ExportBookmarksToPDFDestination`**: Preserves document bookmarks

### Dependencies
- **libreoffice-convert**: v1.6.1 - Node.js wrapper for LibreOffice
- **LibreOffice**: Headless conversion engine

## Integration Points

### Database Models
The PDF conversion is integrated into these models via `beforeCreate` hooks:

1. **ProgramFiles** (`src/models/program-files.ts`)
2. **ApplicationFiles** (`src/models/application-files.ts`)
3. **ClientFiles** (`src/models/client-files.ts`)
4. **AwardFiles** (`src/models/award-files.ts`)

### Workflow Integration
1. User uploads DOCX file
2. Database record creation triggers `beforeCreate` hook
3. `convertToPDF()` function processes the file
4. Both original and PDF files are uploaded to S3
5. URLs are stored in database

## Testing

### Test Suite
Comprehensive test suite located in `src/tests/file-conversion.test.ts`:

- **Basic Conversion**: Verifies DOCX to PDF conversion
- **Link Preservation**: Tests various link types
- **Performance**: Measures conversion times
- **Error Handling**: Tests failure scenarios

### Test Files Required
Create these test files in `src/tests/test-files/`:
- `sample.docx` - Basic conversion test
- `http-links.docx` - HTTP/HTTPS links
- `partial-links.docx` - Domain-only links
- `email-links.docx` - Email addresses
- `large-sample.docx` - Performance testing

### Running Tests
```bash
npm test -- file-conversion.test.ts
```

## Performance Characteristics

### Conversion Times
- **Small files** (<1MB): 5-15 seconds
- **Medium files** (1-5MB): 15-30 seconds
- **Large files** (>5MB): 30-60 seconds

### Resource Usage
- **CPU Intensive**: LibreOffice conversion requires significant CPU
- **Memory Usage**: Proportional to document size
- **Disk I/O**: Temporary files created during processing

## Error Handling

### Common Errors
- **Invalid DOCX Format**: Corrupted or non-DOCX files
- **LibreOffice Failures**: Conversion engine errors
- **Memory Limits**: Large files exceeding available memory
- **Timeout Issues**: Long-running conversions

### Error Recovery
- All errors are logged with detailed information
- Failed conversions prevent database record creation
- Temporary files are cleaned up on failure
- User receives meaningful error messages

## Configuration Options

### Environment Variables
- **CONVERSION_TIMEOUT**: Maximum conversion time (default: 60 seconds)
- **MAX_FILE_SIZE**: Maximum DOCX file size (default: 50MB)
- **TEMP_DIR**: Temporary file directory (default: ./temp)

### LibreOffice Options
The PDF export filter can be customized by modifying the filter string in the `convertToPDF` function. Available options include:

- **Quality**: PDF compression quality
- **Security**: Password protection and permissions
- **Metadata**: Document properties and metadata
- **Forms**: Interactive form handling

## Troubleshooting

### Common Issues

#### Links Not Clickable
- **Cause**: Incorrect LibreOffice parameters
- **Solution**: Verify PDF export filter configuration
- **Check**: Ensure `ConvertOOoTargetToPDFTarget` is set to `true`

#### Conversion Failures
- **Cause**: Corrupted DOCX files or LibreOffice issues
- **Solution**: Validate DOCX format before conversion
- **Check**: LibreOffice installation and permissions

#### Performance Issues
- **Cause**: Large files or insufficient resources
- **Solution**: Implement queue-based processing
- **Check**: Server CPU and memory availability

### Debugging Steps
1. Check LibreOffice installation: `libreoffice --version`
2. Verify file format: Ensure valid DOCX structure
3. Test with minimal file: Use simple DOCX for testing
4. Check logs: Review conversion error messages
5. Monitor resources: CPU and memory usage during conversion

## Future Enhancements

### Planned Improvements
- **Queue System**: Background processing for large files
- **Progress Tracking**: Real-time conversion status
- **Batch Processing**: Multiple file conversion
- **Format Support**: Additional input formats (DOC, RTF)

### Advanced Features
- **Custom Templates**: PDF styling and branding
- **Watermarking**: Automatic watermark application
- **Digital Signatures**: PDF signing capabilities
- **OCR Integration**: Text recognition for scanned documents

## Security Considerations

### File Validation
- **Format Checking**: Verify DOCX file structure
- **Size Limits**: Prevent resource exhaustion
- **Content Scanning**: Malware detection integration
- **Access Control**: Authenticated file operations only

### Data Protection
- **Temporary Files**: Secure cleanup after processing
- **Error Logging**: Sanitized error messages
- **File Permissions**: Restricted access to converted files
- **Audit Trail**: Complete operation logging

## Monitoring and Metrics

### Key Metrics
- **Conversion Success Rate**: Percentage of successful conversions
- **Average Conversion Time**: Performance tracking
- **Error Frequency**: Failure rate monitoring
- **Resource Usage**: CPU and memory consumption

### Alerting
- **Failed Conversions**: Immediate notification
- **Performance Degradation**: Slow conversion alerts
- **Resource Exhaustion**: High usage warnings
- **Queue Backlog**: Processing delay notifications

## Support and Maintenance

### Regular Maintenance
- **LibreOffice Updates**: Keep conversion engine current
- **Dependency Updates**: Update npm packages
- **Performance Tuning**: Optimize conversion parameters
- **Log Rotation**: Manage conversion logs

### Support Resources
- **Documentation**: This README and related docs
- **Test Suite**: Comprehensive testing framework
- **Error Logs**: Detailed failure information
- **Performance Metrics**: Conversion statistics

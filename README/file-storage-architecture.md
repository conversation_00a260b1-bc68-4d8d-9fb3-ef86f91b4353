# File Storage Architecture

## Overview

The admin system uses a comprehensive file storage architecture that handles both original DOCX files and their converted PDF counterparts. The system is built on AWS S3 for cloud storage with CloudFront CDN for efficient file delivery.

## Storage Components

### AWS S3 Storage
- **Primary Storage**: All files are stored in AWS S3 buckets
- **CDN Integration**: CloudFront CDN provides fast global file delivery
- **File Types**: Supports DOCX, PDF, and other document formats

### Database Models

The system uses four main Sequelize models for file management:

#### 1. ProgramFiles (`src/models/program-files.ts`)
- Stores program-related documents
- Automatic PDF conversion via `beforeCreate` hook
- Both original DOCX and converted PDF are uploaded to S3

#### 2. ApplicationFiles (`src/models/application-files.ts`)
- Handles application-specific documents
- Similar PDF conversion workflow

#### 3. ClientFiles (`src/models/client-files.ts`)
- Manages client document storage
- Follows same conversion pattern

#### 4. AwardFiles (`src/models/award-files.ts`)
- Stores award-related documentation
- Implements identical file handling

## File Processing Workflow

### Upload Process
1. **File Upload**: Original DOCX file is uploaded via Express middleware
2. **Database Creation**: File record is created in appropriate model
3. **beforeCreate Hook**: Automatically triggered during record creation
4. **PDF Conversion**: `convertToPDF()` function processes the DOCX file
5. **S3 Upload**: Both original DOCX and converted PDF are uploaded to S3
6. **URL Storage**: S3 URLs are stored in database fields

### PDF Conversion Details
- **Library**: Uses `libreoffice-convert` npm package (v1.6.1)
- **Engine**: LibreOffice headless conversion
- **Enhanced Features**: Preserves hyperlinks including partial URLs (e.g., "example.com")
- **Filter Parameters**: Uses specific PDF export options for link preservation

## Database Schema

### Common File Fields
Each file model contains these standard fields:
- `originalFileUrl`: S3 URL for the original DOCX file
- `pdfFileUrl`: S3 URL for the converted PDF file
- `fileName`: Original filename
- `fileSize`: File size in bytes
- `uploadDate`: Timestamp of upload

### Hooks Implementation
- **beforeCreate**: Handles file upload and PDF conversion
- **beforeDestroy**: Manages file cleanup from S3 when records are deleted

## Storage Locations

### S3 Bucket Structure
```
bucket-name/
├── program-files/
│   ├── original/
│   └── pdf/
├── application-files/
│   ├── original/
│   └── pdf/
├── client-files/
│   ├── original/
│   └── pdf/
└── award-files/
    ├── original/
    └── pdf/
```

### File Naming Convention
- Original files: `{timestamp}-{index}_{originalFileName}.docx`
- PDF files: `{timestamp}-{index}_{originalFileName}.pdf`

## File Access and Delivery

### CloudFront CDN
- **Purpose**: Provides fast, cached access to files globally
- **Benefits**: Reduced latency, bandwidth optimization
- **Integration**: Seamless with S3 bucket structure

### URL Generation
- Files are accessible via CloudFront URLs
- URLs are stored in database for quick retrieval
- Direct S3 access is also available for administrative purposes

## Retention and Cleanup

### Automatic Cleanup
- **beforeDestroy Hook**: Automatically removes files from S3 when database records are deleted
- **Orphan Prevention**: Ensures no orphaned files remain in S3

### Current Retention Policy
- Files are retained indefinitely unless explicitly deleted
- No automatic expiration or archival policies currently implemented
- Manual cleanup required for bulk file management

## Security and Access Control

### S3 Permissions
- Bucket policies control access to stored files
- CloudFront provides additional security layer
- Files are not publicly accessible without proper authentication

### Database Security
- File URLs are stored securely in database
- Access controlled through application authentication
- No direct file system access required

## Performance Considerations

### PDF Conversion
- LibreOffice conversion is CPU-intensive
- Processing time varies with document complexity
- Consider queue-based processing for high-volume scenarios

### Storage Optimization
- CloudFront caching reduces S3 requests
- Efficient file naming prevents conflicts
- Automatic cleanup prevents storage bloat

## Monitoring and Maintenance

### File Integrity
- Database hooks ensure consistency between records and files
- S3 versioning can be enabled for additional protection
- Regular audits recommended for large-scale deployments

### Error Handling
- PDF conversion errors are logged and thrown
- S3 upload failures prevent database record creation
- Rollback mechanisms maintain data integrity

-- Multiple awards can be regenerated at once by changing the award_ids array

INSERT INTO award_budget_entries
(award_id, name, award_amount, award_expended, award_balance, enabled, "createdAt", "updatedAt", match_amount, match_expended, match_balance)
WITH budget_categories AS (
  SELECT unnest(ARRAY['Personnel', 'Fringe Benefits', 'Travel', 'Equipment', 'Supplies',
                      'Contractual', 'Construction', 'Other', 'Total Direct Charges', 'Indirect Charges']) AS name
),
award_ids AS (
  SELECT unnest(ARRAY[1, 63, 64, 65]) AS id
)
SELECT
  a.id,
  b.name,
  0, -- award_amount
  0, -- award_expended
  0, -- award_balance
  true, -- enabled
  NOW(), -- createdAt
  NOW(), -- updatedAt
  0, -- match_amount
  0, -- match_expended
  0  -- match_balance
FROM award_ids a
CROSS JOIN budget_categories b;

-- For Awards: 1,64,65,63

-- budgetEntryCategories: [
--   'Personnel',
--   'Fringe Benefits',
--   'Travel',
--   'Equipment',
--   'Supplies',
--   'Contractual',
--   'Construction',
--   'Other',
--   'Total Direct Charges',
--   'Indirect Charges',
-- ],

-- INSERT INTO award_budget_entries 
--   (awardId, name, awardAmount, awardExpended, awardBalance, matchAmount, matchExpended) 
-- VALUES (1, 'Personnel', 0, 0, 0, 0, 0),
-- (1, 'Fringe Benefits', 0, 0, 0, 0, 0),
-- (1, 'Travel', 0, 0, 0, 0, 0),
-- (1, 'Equipment', 0, 0, 0, 0, 0),
-- (1, 'Supplies', 0, 0, 0, 0, 0),
-- (1, 'Contractual', 0, 0, 0, 0, 0),
-- (1, 'Construction', 0, 0, 0, 0, 0),
-- (1, 'Other', 0, 0, 0, 0, 0),
-- (1, 'Total Direct Charges', 0, 0, 0, 0, 0),
-- (1, 'Indirect Charges', 0, 0, 0, 0, 0);
